# Releasing date-fns

1. First, make sure that the library is built by running `./scripts/build/build.sh` and committing and pushing any change you would have.

2. Then add the changelog entry generated by `npx tsx scripts/release/buildChangelog.ts` to (CHANGELOG.md)[../CHANGELOG.md]. Make sure that the output is valid Markdown and fix if there're any errors. Commit and push the file.

3. Using the version that the changelog script generated, run the command:

   ```bash
   env VERSION="vX.XX.X" APP_ENV="production" GOOGLE_APPLICATION_CREDENTIALS="secrets/production/key.json" ./scripts/release/release.sh
   ```

   The script will change `package.json`. **Do not commit the change, and reset it instead**.

4. Now when the package is published, go to [GitHub Releases](https://github.com/date-fns/date-fns/releases) and draft a new version using the changelog entry you generated earlier.

5. Finally, write an announce tweet using the created GitHub release as the tweet link.

You're done, great job!
